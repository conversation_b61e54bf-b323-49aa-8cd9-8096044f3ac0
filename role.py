# -*- coding: utf-8 -*-
import time
from datetime import datetime
import requests
from urllib.parse import quote
from tools.general import get_yzm
import re
# {{ AURA-X: Modify - 重新添加threading导入，用于长连接的非阻塞处理. Confirmed via 寸止 }}
import threading
from tools.default_setting import default_speed
from tools.tools_proxy import get_proxy
from bs4 import BeautifulSoup

# 控制是否为debug模式,如果是,则MYJ.log正常运行,否则不运行
debug = True


class MYJ:
    def __init__(self, config):
        # 内置默认配置
        self.delay = default_speed
        self.patterns = {
            'npc': re.compile(r"p\._getNpc\(([^)]*)\)"),
            'modify': re.compile(r'<[A-Za-z0-9 ="\':;/_\-.]*>'),
            'petLv': re.compile(r"petLv=(\d+);"),   # 宠物等级
            'nowMap': re.compile(r'nowMap\s*=\s*"([^"]+)"'),    # 当前所在地图
            'room': re.compile(r'room\s*=\s*"([^"]+)"'),    # 当前所在房间
            'hp_left': re.compile(r"<span id='hpLine_left_no'[^>]*>(\d+)</span>"),  # 剩余hp
            'hp_max': re.compile(r"<span id='hpLine_left_max_no'[^>]*>(\d+)</span>"),   # 最大hp
            'sp_left': re.compile(r"<span id='mpLine_left_no'[^>]*>(\d+)</span>"),  # 剩余sp
            'sp_max': re.compile(r"<span id='mpLine_left_max_no'>(\d+)</span>"),    # 最大sp
            'exp': re.compile(r'<\s*span\s+id\s*=\s*["\']?expBai["\']?\s*>(\d+)\s*</\s*span\s*>', re.IGNORECASE),   # 百分比经验
            'join_role': re.compile(r"selectimg\(\s*(\d+)\s*,\s*'click'\s*,\s*(\d+)[^)]*\)\s*.*?"
                                    r'<div\s+align="center"\s+style="position:relative;top:-10px">(.*?)</div>', re.DOTALL),  # 获取角色代码及序号部分
            # 'skills': re.compile(r"p\.cutArray\[(?:[0-5])\]='(perform.*?)';"),  #
            'petName': re.compile(r'petName\s?=\s?"(.*)";'),    # 宠物名
            'myUserId': re.compile(r'myUserId\s?=\s?"(.*)";'),  # id
            'validateParam': re.compile(r'validateParam\s?=\s?"(.*)";'),    # 链接验证参数
            'combat_start': re.compile(r"p\._combat\s*\(", re.IGNORECASE),      # 进入战斗
            'combat_end': re.compile(r"p\.lost\(\s*p\.petWin\.fighter_2\s*\)", re.IGNORECASE),  # 退出战斗
        }

        # 本地配置获取
        self.fwq = config.get('服务器')
        self.account = config.get('账号')
        self.pwd = config.get('密码')
        self.role_index = config.get('角色序号')
        self.proxies_setting = config.get('代理配置', None)
        # 此处获取的代理存活时长为3分钟
        if self.proxies_setting:
            self.proxies = get_proxy(self.proxies_setting.get('代理账号'), self.proxies_setting.get('代理密码'))
        else:
            self.proxies = None

        # 动态修改
        self.login_status = '未登录'
        self.cookies = None
        self.petId = None
        self.petLv = None
        self.nowMap = None
        self.room = None
        self.petName = None
        self.myUserId = None
        self.validateParam = None
        self.hp_left = None
        self.sp_left = None
        self.hp_max = None
        self.sp_max = None
        self.exp_bai = None
        self.online_url = None
        self.message_box_right_bottom = []
        self.message_box_left_bottom = []
        self.message_box_right_up = []

        # 添加其他需要的属性初始化
        self.in_battle = False
        self.npc_list = []
        self.left_bottom_last_message = ""
        self.exp_now = 0
        self.exp_max = 0
        self.packages = {}
        self.packages_update_time = 0
        self.main_skill = ""
        self.npc_talk_message = ""
        self._js_function_buffer = False
        self._int_compile = re.compile(r'\d+')  # 用于提取数字

    def log(self, *args):
        global debug
        if debug:
            text = ' '.join(str(arg) for arg in args)
            # 原有逻辑，比如写入日志、打印等
            s = f'{datetime.now()}===>{self.account}===>{text}'
            print(s)

    def login(self):
        # 登录错误码
        _error_code = {
            '0': "登录失败,请重新登录",
            '1': "签名验证失败",
            '2': "时间戳过期",
            '3': "参数为空或格式不正确",
            '4': "用户名密码验证未通过",
            '5': "用户已被锁定",
            '6': "密保未通过",
            '7': "cookie验证未通过",
            '8': "token验证未通过",
            '9': "大区验证未通过",
            '11': "验证码错误",
            '12': "验证码为空",
            '999': "系统异常，登录失败"
        }
        # 登录初始cookie
        _cookies = {'mopet_logon': '123'}
        # 登录请求头
        _headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': f'http://{self.fwq}.pet.imop.com',
            'Referer': f'http://{self.fwq}.pet.imop.com/login.html',
            'Connection': 'keep-alive',
            'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
        }
        # 账号密码,账号支持中文
        _data = {
            'user_name': quote(self.account, encoding="gbk"),
            'password': self.pwd,
        }
        # 登录
        while True:
            # 每次登陆重新获取一次验证码
            _data['checkcode'] = get_yzm(proxies=self.proxies)  
            # 发送登录请求
            response = requests.post(
                url=f'http://{self.fwq}.pet.imop.com/LoginAction.jsp',
                cookies=_cookies,
                headers=_headers,
                data=_data,
                proxies=self.proxies
            )
            # print(response.text)
            if r'document.location="/pet.jsp"' in response.text:
                self.cookies = response.cookies
                break
            else:
                for _code in _error_code.keys():
                    if f'errCode={_code}"' in response.text:
                        if _code == '11':
                            # 错误码为11,验证码错误,等待1秒重新获取验证码登录
                            time.sleep(1)
                            continue
                        else:
                            print(f'出现非验证码错误,错误类型为=>{_error_code[_code]}')
                            return _error_code[_code]
        # 进入角色
        while True:
            _join_role_headers = {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'Origin': f'http://{self.fwq}.pet.imop.com',
                            'Referer': f'http://{self.fwq}.pet.imop.com/login.html',
                            'Connection': 'keep-alive',
                            'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                        }
            _res = requests.get(
                                url=f'http://{self.fwq}.pet.imop.com/action.jsp?action=changerole',
                                cookies=self.cookies,
                                headers=_join_role_headers,
                                proxies=self.proxies
                            )
            _res = requests.get(
                    url=f'http://{self.fwq}.pet.imop.com/pet.jsp',
                    cookies=self.cookies,
                    headers=_join_role_headers,
                    proxies=self.proxies
                )
            if 'selectimg' in _res.text:
                break
        # 打印列表角色开始
        self.log('获取角色成功')
        role_boxes = self.patterns['join_role'].findall(_res.text)
        self.log('角色信息打印-开始')
        for role in role_boxes:
            print(f"""角色序号[{role[0]}]角色名[{role[2]}]角色id[{role[1]}]""")
        self.log('角色信息打印-完成')
        # 打印列表角色结束
        # 确认角色Id开始
        for role in role_boxes:
            if int(role[0]) == int(self.role_index):
                self.petId = role[1]
                break
        # 确认角色Id结束

        # 进入角色url
        _pet_url = f'http://{self.fwq}.pet.imop.com/pet.jsp?petid={self.petId}'
        _join_role_headers['Referer'] = f'http://{self.fwq}.pet.imop.com/pet.jsp?petid={self.petId}'

        # 确保进入角色
        while True:
            # 此处是确保进入角色,showWorldMap是展现当前地图
            _resp = requests.get(url=_pet_url, cookies=self.cookies)
            if 'showWorldMap' in _resp.text:
                break
            time.sleep(1)
        
        # 初始化属性
        while True:
            response = requests.get(url=_pet_url, cookies=self.cookies)
            t = response.text
            try:
                self.log('初始化属性值')
                self.petLv = self.patterns['petLv'].search(t).group(1)
                self.nowMap = self.patterns['nowMap'].search(t).group(1)
                self.room = self.patterns['room'].search(t).group(1)
                self.petName = self.patterns['petName'].search(t).group(1)
                self.myUserId = self.patterns['myUserId'].search(t).group(1)
                self.validateParam = self.patterns['validateParam'].search(t).group(1)
                self.hp_left = self.patterns['hp_left'].search(t).group(1)
                self.sp_left = self.patterns['sp_left'].search(t).group(1)
                self.hp_max = self.patterns['hp_max'].search(t).group(1)
                self.sp_max = self.patterns['sp_max'].search(t).group(1)
                self.exp_bai = int(re.compile(r'<\s*span\s+id\s*=\s*["\']?expBai["\']?\s*>(\d+)\s*</\s*span\s*>', re.IGNORECASE).search(t).group(1))
                self.online_url = f'http://{self.fwq}.pet.imop.com:8080/io/{self.myUserId}&{self.validateParam}'
                self.log('属性初始化完成')
                break
            except:
                pass
        return True

    def _ret_packages(self, line):
        """解析背包内容"""
        try:
            # 这里实现背包解析逻辑
            return {}  # 返回空字典作为占位符
        except Exception as e:
            self.log(f"解析背包出错: {e}")
            return {}

    def _extract_js_functions(self, text):
        """
        {{ AURA-X: Add - 新增JavaScript函数提取方法，解决正则表达式分割问题. Confirmed via 用户反馈 }}
        使用状态机方法提取JavaScript函数调用，正确处理嵌套括号和缺少分号的情况
        """
        functions = []
        i = 0
        while i < len(text):
            # 查找 p. 开头的函数
            if i < len(text) - 1 and text[i:i+2] == 'p.':
                start = i
                # 找到函数名结束位置（遇到左括号）
                j = i + 2
                while j < len(text) and text[j] not in '(':
                    j += 1

                if j < len(text) and text[j] == '(':
                    # 找到匹配的右括号
                    paren_count = 1
                    k = j + 1
                    while k < len(text) and paren_count > 0:
                        if text[k] == '(':
                            paren_count += 1
                        elif text[k] == ')':
                            paren_count -= 1
                        k += 1

                    # 检查是否以分号结尾，如果没有分号也接受
                    if k < len(text) and text[k] == ';':
                        functions.append(text[start:k+1])
                        i = k + 1
                    else:
                        # 没有分号的情况，直接到右括号结束
                        functions.append(text[start:k])
                        i = k
                else:
                    i += 1
            else:
                i += 1
        return functions

    def _process_js_function_with_buffer(self, data):
        """
        {{ AURA-X: Add - 处理可能分段的JavaScript函数. Confirmed via 用户反馈 }}
        处理分段接收的JavaScript函数，特别是p.showI这样的大型函数
        """
        # 如果是p.showI开头，开始缓存
        if data.startswith('p.showI'):
            self._js_function_buffer = data
            # 检查是否是完整的函数（以分号结尾）
            if data.endswith(');'):
                # 完整函数，直接处理
                complete_function = self._js_function_buffer
                self._js_function_buffer = ""
                return complete_function
            else:
                # 不完整，等待更多数据
                return None

        # 如果有缓存且当前数据不是新函数开头
        elif self._js_function_buffer and not data.startswith('p.'):
            # 继续拼接
            self._js_function_buffer += data
            # 检查是否完整
            if self._js_function_buffer.endswith(');'):
                # 完整函数，返回并清空缓存
                complete_function = self._js_function_buffer
                self._js_function_buffer = ""
                return complete_function
            else:
                # 仍不完整，继续等待
                return None

        # 其他情况，直接返回原数据
        return data

    def _ret_packages(self, _s):
        def _package_log(*args):
            # with open(r'C:\Project\py\MemoryLastDance\temp_files\背包测试.txt', 'a', encoding='utf-8') as f:
            #     f.write(' '.join(str(arg) for arg in args) + '\n')
            pass
        # 从字符串中提取JavaScript数组部分
        # {{ AURA-X: Modify - 修复背包解析，支持 p.showI 和 p.showIHide 两种格式. Confirmed via 用户反馈 }}
        # 找到 p.showI([ 或 p.showIHide([ 和 ],'<span 之间的内容
        # 尝试两种可能的格式
        start_markers = ["p.showIHide([", "p.showI(["]
        end_marker = "],'<span"

        start_pos = -1
        start_marker_used = ""

        # 查找匹配的开始标记
        for marker in start_markers:
            pos = _s.find(marker)
            if pos != -1:
                start_pos = pos
                start_marker_used = marker
                break

        _package_log('尝试的标记:', start_markers)
        _package_log('使用的标记:', start_marker_used)
        _package_log('end_marker', end_marker)
        _package_log('start_pos', start_pos)
        end_pos = _s.find(end_marker)
        if end_pos == -1:
            end_pos = len(_s)
        _package_log('end_pos', end_pos)
        if start_pos != -1 and end_pos != -1:
            # 提取数组部分（包括外层的方括号）
            array_str = _s[start_pos + len(start_marker_used) - 1:end_pos + 1]
            try:
                # 将JavaScript数组转换为Python列表
                # 替换JavaScript布尔值
                python_str = array_str.replace('true', 'True').replace('false', 'False')
                # 使用eval解析（注意：在生产环境中要小心使用eval）
                parsed_data = eval(python_str)
                # 打印前几个物品信息
                return parsed_data
            except Exception as e:
                print(f"解析失败: {e}")
                # {{ AURA-X: Modify - 修复备用方案返回格式，确保返回解析后的列表而不是字符串. Confirmed via 用户反馈 }}
                item_pattern = r"\['[^']*',\d+,'[^']*',\d+,'[^']*','[^']*',(?:true|false),(?:true|false),'[^']*','[^']*',(?:true|false),'[^']*',(?:true|false),(?:true|false),(?:true|false),'[^']*',(?:true|false),(?:true|false)\]"
                item_matches = re.findall(item_pattern, _s)
                print(f"使用备用方案找到 {len(item_matches)} 个物品字符串")

                # 将匹配到的字符串转换为实际的列表
                parsed_items = []
                for match in item_matches:
                    try:
                        # 替换JavaScript布尔值并解析
                        python_str = match.replace('true', 'True').replace('false', 'False')
                        item_data = eval(python_str)
                        parsed_items.append(item_data)
                    except Exception as parse_error:
                        print(f"解析单个物品失败: {parse_error}, 物品字符串: {match[:50]}...")
                        continue

                print(f"备用方案成功解析 {len(parsed_items)} 个物品")
                return parsed_items
        else:
            return None

    def pull_online(self):
        def _modify_html_str(_s):
            _ss = BeautifulSoup(_s, 'html.parser')
            return ''.join(tag.get_text() for tag in _ss.find_all('script'))

        def _pull_online():
            # {{ AURA-X: Modify - 将异步函数改为同步函数，移除async关键字. Confirmed via 寸止 }}
            while True:
                # {{ AURA-X: Modify - 使用with语句正确管理连接资源，长连接不使用代理. Confirmed via 寸止 }}
                try:
                    with requests.get(self.online_url, cookies=self.cookies, stream=True) as response:
                        # {{ AURA-X: Modify - 使用iter_content逐字节读取，实时处理整句数据. Confirmed via 寸止 }}
                        buffer = ""
                        for chunk in response.iter_content(chunk_size=1, decode_unicode=True):
                            if chunk:
                                buffer += chunk
                                # 检查是否有完整的行（以换行符结尾）
                                while '\n' in buffer:
                                    line, buffer = buffer.split('\n', 1)
                                    line = line.strip()
                                    if line:  # 过滤空行
                                        print(line)
                                        # with open(os.path.join(os.getcwd(), 'temp_files', '日志.txt'), 'a', encoding='utf-8') as f:
                                        #     f.write(line + '\n')
                                        # print('line', line)
                                        if 'p.showI' in line:
                                            # print('触发了')
                                            # with open(r'C:\Project\py\MemoryLastDance\temp_files\背包测试.txt', 'a', encoding='utf-8') as f:
                                            #     f.write(line + '\n\n\n')
                                            self.packages = self._ret_packages(line)
                                            self.packages_update_time = time.time()
                                            # print('背包:', self.packages)
                                        _temp_s = _modify_html_str(line)
                                        _li = self._extract_js_functions(_temp_s)
                                        for data in _li:
                                            if data.startswith('p.addCM("'):
                                                s = data.replace('p.addCM("', '').replace('")', '')
                                                s = _modify_html_str(s)
                                                self.message_box_right_up.append(s)
                                                self.log('右上框', s)
                                                continue
                                            elif data.startswith('p.initWorker()'):
                                                # 登录
                                                continue
                                            elif data.startswith('p.cls()'):
                                                # 房间
                                                continue
                                            elif data.startswith('p.offOpenWin'):
                                                # 关闭窗口
                                                continue
                                            elif data.startswith("p.cps('zone')"):
                                                # 房间
                                                continue
                                            elif data.startswith('p.clsMes("npcChatReader")'):
                                                # NPC聊天框
                                                continue
                                            elif data.startswith('p._roomDesc'):
                                                # self.log('指定函数跳过输出')
                                                continue
                                            elif data.startswith('parent.reOnlineNum'):
                                                # self.log('在线状态,不处理')
                                                continue
                                            elif data.startswith('p.addUser'):
                                                # self.log('当前房间增加player,暂不处理')
                                                continue
                                            elif data.startswith('p.delUser'):
                                                # self.log('当前房间减少player,暂不处理')
                                                continue
                                            elif data.startswith('p.showRen'):
                                                # self.log('NPC图片,暂不处理')
                                                continue
                                            elif data.startswith('p.closeRen'):
                                                # self.log('NPC图片,暂不处理')
                                                continue
                                            elif data.startswith('p.showAlert'):
                                                # self.log('白底小框提示信息,暂不处理')
                                                continue
                                            elif 'p._combat' in data:
                                                self.in_battle = True
                                                # self.log('进入战斗')
                                            elif data.startswith('p.state'):
                                                # 战斗相关,不处理
                                                continue
                                            elif data.startswith('p.att'):
                                                # 战斗相关,不处理
                                                continue
                                            elif 'p.lost' in data or '已经死了!' in data or '被你杀死了...' in data or r"p.setFightTaskImg('null')" in data:
                                                # 原有脱战判断
                                                pass
                                                # self.send_single_order('look', 0)
                                                # self.log('结束战斗')
                                            elif data.startswith('p.win'):
                                                continue
                                            elif data.startswith('p.addNpcs('):
                                                self.npc_list = [i.replace("'", '').replace(" ", '').split(',') for i in self.patterns['npc'].findall(data)]
                                                # self.log('调整npc列表', self.npc_list)
                                            elif data.startswith('p.addRM('):
                                                if 'Desc' in data:
                                                    # self.log('没用的函数暂不处理')
                                                    continue
                                                else:
                                                    _ = '''p.addRM('''
                                                    raw_html = data[data.find(_) + 1 + len(_):-3]
                                                    _soup = BeautifulSoup(raw_html, 'html.parser')
                                                    _clean_text = _soup.get_text(strip=True).replace(' ', '')
                                                    # print(_clean_text)
                                                    self.message_box_left_bottom.append(_clean_text)
                                                    self.log('左下框', _clean_text)
                                                    self.left_bottom_last_message = _clean_text
                                            elif data.startswith(r'''p.addMessage('roomReader',"'''):
                                                if data.endswith('",false);'):
                                                    _raw_html = data[data.find(r'''p.addMessage('roomReader',"''') + len(r'''p.addMessage('roomReader',"'''):data.find(',false);') - 1]
                                                    _soup_left_down = BeautifulSoup(_raw_html, 'html.parser')
                                                    _clean_text = _soup_left_down.get_text().replace(' ', '')
                                                    self.message_box_left_bottom.append(_clean_text)
                                                    self.log('左下框', _clean_text)
                                                    self.left_bottom_last_message = _clean_text
                                                else:
                                                    print("待处理左下框原始数据$", data)
                                            elif data.startswith('p.addMY("'):
                                                print("右下框原始数据$", data)
                                                s = data.replace('p.addMY("', '').replace('")', '')
                                                self.message_box_right_bottom.append(s)
                                                self.log('右下框', s)
                                            elif data.startswith("p.setRoom('"):
                                                _t_index = data.find("'") + 1
                                                self.room = data[_t_index:data.find("'", _t_index)]
                                                self.log('当前房间', self.room)
                                                self.in_battle = False
                                            elif data.startswith("p.changeMap('"):
                                                _t_index = data.find("'") + 1
                                                self.nowMap = data[_t_index:data.find("'", _t_index)]
                                                self.log('当前地图', self.nowMap)
                                    elif data.startswith("p.setLine('hpLine_left"):
                                        self.hp_left = int(self._int_compile.findall(data)[-1])
                                        # self.log('剩余血量', self.hp_left)
                                    elif data.startswith("p.setLine('mpLine_left"):
                                        self.sp_left = int(self._int_compile.findall(data)[-1])
                                        # self.log('剩余蓝量', self.sp_left)
                                    elif data.startswith("p.setLine('hpLine_right"):
                                        # self.log('对手剩余血量,不处理')
                                        continue
                                    elif data.startswith("p.setLine('mpLine_right"):
                                        # self.log('对手剩余蓝量,不处理')
                                        continue
                                        # self.log('剩余蓝量', self.sp_left)
                                    elif data.startswith("p.setMaxHP("):
                                        self.hp_max = int(self._int_compile.findall(data)[-1])
                                        # self.log('最大血量', self.hp_max)
                                    elif data.startswith("p.setMaxSP("):
                                        self.sp_max = int(self._int_compile.findall(data)[-1])
                                        # self.log('最大蓝量', self.sp_max)
                                    elif data.startswith("p.setExp"):
                                        _s = data.replace('p.setExp(', '').replace(');', '').split(',')
                                        self.exp_now = int(_s[0])
                                        self.exp_max = int(_s[2])
                                    elif data.startswith('p._petinfo'):
                                        for _s in ['龙葵匕刃', '飓风刀法', '幽冥爪', '十字剑', '霸王枪', '暗魔法', '圣魔法', '火系法术', '冰系法术', '风系法术']:
                                            if _s in data:
                                                self.main_skill = _s
                                                self.log('当前主技能', self.main_skill)
                                                break
                                    elif data.startswith('p.addNPCC('):
                                        _ = 'p.addNPCC("'
                                        _raw_html = data[data.find(_) + len(_):-3]
                                        _soup = BeautifulSoup(_raw_html, 'html.parser')
                                        _clean_text = _soup.get_text(strip=True).replace(' ', '')
                                        self.npc_talk_message = _clean_text
                                        # self.log('NPC聊天框', _clean_text)
                                    elif data.startswith('p.showI') or self._js_function_buffer:
                                        continue
                                    else:
                                        self.log(f'未设置格式 {data}')
                except Exception as e:
                    # {{ AURA-X: Modify - 修复异常处理的缩进和作用域. Confirmed via 寸止 }}
                    self.log(e)
                    self.login_status = '离线'
                    self.log('连接断开，30秒后重新连接')
                    # {{ AURA-X: Modify - 将异步sleep改为同步sleep. Confirmed via 寸止 }}
                    time.sleep(30)

        # {{ AURA-X: Modify - 使用线程运行同步长连接，避免阻塞主程序. Confirmed via 寸止 }}
        if self.login():
            threading.Thread(target=_pull_online, daemon=True).start()
